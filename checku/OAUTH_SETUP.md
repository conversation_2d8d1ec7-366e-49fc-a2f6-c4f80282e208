# OAuth SSO Setup Guide

This guide explains how to set up OAuth SSO (Single Sign-On) with Google and LinkedIn for the CheckU application.

## Prerequisites

1. Google Cloud Console account
2. PostgreSQL database running
3. Node.js 22.12 and pnpm installed

## Google OAuth Setup

### 1. Create Google OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:3000/api/auth/callback/google` (for NextAuth)
     - `http://localhost:4000/api/auth/google/callback` (for our API)
   - Save the Client ID and Client Secret

### 2. Environment Variables

#### API (.env)

```bash
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/checku_db?schema=public"

# Application
PORT=4000
NODE_ENV=development

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GOOGLE_CALLBACK_URL="http://localhost:4000/auth/google/callback"

# Frontend URL
FRONTEND_URL="http://localhost:3000"

# JWT (optional)
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="7d"
```

#### Web App (.env.local)

```bash
# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# API Configuration
NEXT_PUBLIC_API_URL="http://localhost:4000"
```

## Database Setup

1. Make sure PostgreSQL is running
2. Run the database migration:

```bash
cd apps/api
pnpm db:migrate
```

## Running the Application

1. Start the API:

```bash
cd apps/api
pnpm dev
```

2. Start the web app:

```bash
cd apps/web
pnpm dev
```

## How It Works

### Architecture

1. **Frontend (Next.js + NextAuth)**: Handles the OAuth flow with Google (port 3000)
2. **API (NestJS)**: Manages user data and provides OAuth endpoints (port 4000)
3. **Database (PostgreSQL + Prisma)**: Stores user information

### Flow

1. User clicks "Sign in with Google" on the frontend
2. NextAuth redirects to Google OAuth
3. Google redirects back to NextAuth with user data
4. Frontend calls our API to create/find the user
5. API stores user data in the database
6. User is signed in and can access the application

### API Endpoints

- `GET /auth/google` - Initiates Google OAuth flow
- `GET /auth/google/callback` - Handles Google OAuth callback
- `POST /auth/google/signup` - Creates a new user from OAuth data
- `POST /auth/google/signin` - Signs in existing OAuth user

### Database Schema

The User model includes OAuth fields:

- `provider`: OAuth provider ('google', 'local')
- `providerId`: OAuth provider's user ID
- `picture`: Profile picture URL

## Testing

1. Navigate to `http://localhost:3000`
2. Click "Continue with Google"
3. Complete the Google OAuth flow
4. You should be redirected back and see your user information

## Troubleshooting

### Common Issues

1. **OAuth redirect URI mismatch**: Make sure the redirect URIs in Google Console match your environment variables
2. **Database connection issues**: Verify your DATABASE_URL is correct
3. **CORS issues**: Make sure FRONTEND_URL is set correctly in the API

### Debug Tips

1. Check browser console for errors
2. Check API logs for authentication errors
3. Verify environment variables are loaded correctly
4. Test database connection with `pnpm db:studio`

## Security Considerations

1. Keep your Google Client Secret secure
2. Use HTTPS in production
3. Set proper CORS policies
4. Validate all OAuth data on the server side
5. Consider implementing rate limiting for OAuth endpoints
