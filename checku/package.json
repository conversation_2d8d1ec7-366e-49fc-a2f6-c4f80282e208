{"name": "checku", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:full": "pnpm db:up && pnpm dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "db:up": "docker-compose up -d postgres", "db:down": "docker-compose down", "db:reset": "docker-compose down -v && docker-compose up -d postgres", "db:logs": "docker-compose logs postgres", "pgadmin:up": "docker-compose up -d", "pgadmin:open": "open http://localhost:5050"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": "22.12"}}