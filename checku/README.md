# CheckU - OAuth SSO Application

A full-stack application with Gmail SSO authentication built with Turborepo.

## Prerequisites

- Node.js 22.12 (use `nvm use` if you have nvm installed)
- pnpm 9.0.0+
- PostgreSQL database

## What's inside?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `api`: a [NestJS](https://nestjs.com/) API with OAuth authentication (port 4000)
- `web`: a [Next.js](https://nextjs.org/) web application with NextAuth (port 3000)
- `@repo/ui`: a React component library shared by applications
- `@repo/eslint-config`: `eslint` configurations (includes `eslint-config-next` and `eslint-config-prettier`)
- `@repo/typescript-config`: `tsconfig.json`s used throughout the monorepo

Each package/app is 100% [TypeScript](https://www.typescriptlang.org/).

### Utilities

This Turborepo has some additional tools already setup for you:

- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Prettier](https://prettier.io) for code formatting

## Quick Start

1. **Install dependencies:**

```bash
pnpm install
```

2. **Set up environment variables:**

```bash
# Copy example files and configure
cp apps/api/.env.example apps/api/.env
cp apps/web/.env.example apps/web/.env.local
```

3. **Start the database:**

```bash
pnpm db:up
```

4. **Run database migrations:**

```bash
cd apps/api && pnpm db:migrate
```

5. **Start both applications:**

```bash
pnpm dev
```

This will start:

- API server on http://localhost:4000
- Web application on http://localhost:3000

### Build

To build all apps and packages:

```bash
pnpm build
```

### Other Commands

```bash
pnpm dev:full        # Start database + both apps
pnpm check-types     # Type check all packages
pnpm lint           # Lint all packages
pnpm db:up          # Start PostgreSQL
pnpm db:down        # Stop PostgreSQL
```

## OAuth Setup

For detailed Gmail SSO setup instructions, see [OAUTH_SETUP.md](./OAUTH_SETUP.md).

### Remote Caching

> [!TIP]
> Vercel Remote Cache is free for all plans. Get started today at [vercel.com](https://vercel.com/signup?/signup?utm_source=remote-cache-sdk&utm_campaign=free_remote_cache).

Turborepo can use a technique known as [Remote Caching](https://turborepo.com/docs/core-concepts/remote-caching) to share cache artifacts across machines, enabling you to share build caches with your team and CI/CD pipelines.

By default, Turborepo will cache locally. To enable Remote Caching you will need an account with Vercel. If you don't have an account you can [create one](https://vercel.com/signup?utm_source=turborepo-examples), then enter the following commands:

```
cd my-turborepo
npx turbo login
```

This will authenticate the Turborepo CLI with your [Vercel account](https://vercel.com/docs/concepts/personal-accounts/overview).

Next, you can link your Turborepo to your Remote Cache by running the following command from the root of your Turborepo:

```
npx turbo link
```

## Useful Links

Learn more about the power of Turborepo:

- [Tasks](https://turborepo.com/docs/crafting-your-repository/running-tasks)
- [Caching](https://turborepo.com/docs/crafting-your-repository/caching)
- [Remote Caching](https://turborepo.com/docs/core-concepts/remote-caching)
- [Filtering](https://turborepo.com/docs/crafting-your-repository/running-tasks#using-filters)
- [Configuration Options](https://turborepo.com/docs/reference/configuration)
- [CLI Usage](https://turborepo.com/docs/reference/command-line-reference)
