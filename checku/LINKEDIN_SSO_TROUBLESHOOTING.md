# LinkedIn SSO Troubleshooting Guide

## Current Issue
LinkedIn SSO redirects to `/auth/signin?callbackUrl=...&error=OAuthCallback` indicating an OAuth callback error.

## Root Cause Analysis

The issue is likely due to one of these problems:

### 1. LinkedIn App Configuration
The LinkedIn OAuth app needs to be configured with the correct redirect URIs:

**Required Redirect URIs:**
- `http://localhost:3000/api/auth/callback/linkedin` (for NextAuth)
- `http://localhost:4000/auth/linkedin/callback` (for API)

### 2. Environment Variables
Make sure both applications have the correct LinkedIn credentials:

**Web App (.env.local):**
```
LINKEDIN_CLIENT_ID="86k93uz8r3qywx"
LINKEDIN_CLIENT_SECRET="WPL_AP1.vofH5ngFVg4hbw9Q.WBDqqQ=="
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-change-this-in-production"
```

**API (.env):**
```
LINKEDIN_CLIENT_ID="86k93uz8r3qywx"
LINKEDIN_CLIENT_SECRET="WPL_AP1.vofH5ngFVg4hbw9Q.WBDqqQ=="
LINKEDIN_CALLBACK_URL="http://localhost:4000/auth/linkedin/callback"
```

### 3. LinkedIn API Scopes
LinkedIn has updated their API. The correct scopes are:
- `openid`
- `profile` 
- `email`

## Steps to Fix

### Step 1: Update LinkedIn App Settings
1. Go to [LinkedIn Developer Console](https://www.linkedin.com/developers/apps)
2. Select your app
3. Go to "Auth" tab
4. Update "Authorized redirect URLs for your app":
   - Add: `http://localhost:3000/api/auth/callback/linkedin`
   - Add: `http://localhost:4000/auth/linkedin/callback`
5. Make sure the following products are enabled:
   - "Sign In with LinkedIn using OpenID Connect"

### Step 2: Verify Environment Variables
Check that both apps have the correct LinkedIn credentials and URLs.

### Step 3: Test the Flow
1. Clear browser cache and cookies
2. Restart both applications
3. Try LinkedIn login again

## Debugging Steps

### Check Browser Console
Look for any JavaScript errors during the OAuth flow.

### Check API Logs
Monitor the API console for LinkedIn callback logs:
```bash
# In the API terminal, you should see:
LinkedIn callback received
LinkedIn profile received: { ... }
LinkedIn user data processed: { ... }
```

### Check NextAuth Logs
Monitor the web app console for NextAuth callback logs:
```bash
# In the web app terminal, you should see:
NextAuth signIn callback: { user, account, profile }
```

## Common Issues

### Issue 1: "redirect_uri_mismatch"
- **Cause**: LinkedIn app redirect URIs don't match the callback URLs
- **Fix**: Update LinkedIn app settings with correct redirect URIs

### Issue 2: "invalid_scope"
- **Cause**: Using deprecated LinkedIn API scopes
- **Fix**: Use `openid profile email` scopes

### Issue 3: "OAuthCallback" error
- **Cause**: NextAuth can't process the LinkedIn response
- **Fix**: Check environment variables and LinkedIn app configuration

### Issue 4: No email in profile
- **Cause**: LinkedIn app doesn't have email permission
- **Fix**: Enable "Sign In with LinkedIn using OpenID Connect" product

## Testing Commands

### Test API LinkedIn Endpoint
```bash
curl http://localhost:4000/auth/linkedin
```

### Test NextAuth LinkedIn Endpoint
```bash
curl http://localhost:3000/api/auth/providers
```

## Expected Flow

1. User clicks "Continue with LinkedIn" button
2. NextAuth redirects to LinkedIn OAuth
3. User authorizes the app on LinkedIn
4. LinkedIn redirects to `http://localhost:3000/api/auth/callback/linkedin`
5. NextAuth processes the callback and calls our API
6. API creates/finds user and returns success
7. User is redirected to home page

## If Still Not Working

1. Check LinkedIn app status (make sure it's not in development mode restrictions)
2. Verify LinkedIn app has the correct permissions
3. Try creating a new LinkedIn app with fresh credentials
4. Check if LinkedIn is experiencing API issues
