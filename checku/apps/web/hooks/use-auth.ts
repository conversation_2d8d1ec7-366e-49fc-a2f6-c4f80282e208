"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";

interface ApiUser {
  id: string;
  email: string;
  name?: string;
  provider: string;
  providerId?: string;
  picture?: string;
  createdAt: string;
  updatedAt: string;
}

export function useAuth() {
  const { data: session, status } = useSession();
  const [apiUser, setApiUser] = useState<ApiUser | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (status === "authenticated" && session?.user && session.providerId) {
      handleOAuthSignup();
    }
  }, [status, session]);

  const handleOAuthSignup = async () => {
    if (!session?.user || !session.providerId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Determine provider and endpoint
      const provider = session.provider === "linkedin" ? "linkedin" : "google";

      // Try to sign up first (this will handle both new and existing users)
      const signupResponse = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/${provider}/signup`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: session.user.email,
            name: session.user.name,
            provider: provider,
            providerId: session.providerId,
            picture: session.user.image,
          }),
        }
      );

      if (signupResponse.ok) {
        const data = await signupResponse.json();
        setApiUser(data.user);
      } else {
        // If signup fails, try signin (user might already exist)
        const signinResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/auth/${provider}/signin`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              providerId: session.providerId,
            }),
          }
        );

        if (signinResponse.ok) {
          const data = await signinResponse.json();
          setApiUser(data.user);
        } else {
          const errorData = await signinResponse.json();
          setError(errorData.message || "Authentication failed");
        }
      }
    } catch (err) {
      setError("Network error occurred");
      console.error("Auth error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    session,
    status,
    apiUser,
    isLoading,
    error,
    isAuthenticated: status === "authenticated" && !!apiUser,
  };
}
