import { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import LinkedInProvider from "next-auth/providers/linkedin";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    LinkedInProvider({
      clientId: process.env.LINKEDIN_CLIENT_ID!,
      clientSecret: process.env.LINKEDIN_CLIENT_SECRET!,
      wellKnown: undefined,
      authorization: {
        params: {
          scope: "r_liteprofile r_emailaddress",
        },
      },
      issuer: undefined,
      checks: ["state"],
      client: {
        token_endpoint_auth_method: "client_secret_post",
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      console.log("NextAuth signIn callback:", { user, account, profile });

      if (!account || !user) {
        console.error("Missing account or user data in signIn callback");
        return false;
      }

      try {
        // Call our API to register/login the user
        const provider =
          account.provider === "linkedin" ? "linkedin" : "google";
        const apiUrl =
          process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";

        // First try to sign up (this will handle both new and existing users)
        const signupResponse = await fetch(
          `${apiUrl}/auth/${provider}/signup`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: user.email,
              name: user.name,
              provider: provider,
              providerId: account.providerAccountId,
              picture: user.image,
            }),
          }
        );

        if (signupResponse.ok) {
          const userData = await signupResponse.json();
          console.log("User registered/logged in successfully:", userData);
          return true;
        } else {
          // If signup fails, try signin (user might already exist)
          const signinResponse = await fetch(
            `${apiUrl}/auth/${provider}/signin`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                providerId: account.providerAccountId,
              }),
            }
          );

          if (signinResponse.ok) {
            const userData = await signinResponse.json();
            console.log("User signed in successfully:", userData);
            return true;
          } else {
            console.error("Both signup and signin failed");
            return false;
          }
        }
      } catch (error) {
        console.error("Error in signIn callback:", error);
        return false;
      }
    },
    async jwt({ token, user, account }) {
      if (account && user) {
        token.accessToken = account.access_token;
        token.providerId = account.providerAccountId;
        token.provider = account.provider;
      }
      return token;
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken as string;
      session.providerId = token.providerId as string;
      session.provider = token.provider as string;
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
};
