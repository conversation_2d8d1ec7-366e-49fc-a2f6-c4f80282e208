import { User } from '../entities';
import { Email, UserId, OAuthProvider } from '../value-objects';

export interface UserRepository {
  save(user: User): Promise<User>;
  findById(id: UserId): Promise<User | null>;
  findByEmail(email: Email): Promise<User | null>;
  findByOAuthProvider(provider: OAuthProvider): Promise<User | null>;
  findAll(): Promise<User[]>;
  delete(id: UserId): Promise<void>;
  exists(id: UserId): Promise<boolean>;
  existsByEmail(email: Email): Promise<boolean>;
  existsByOAuthProvider(provider: OAuthProvider): Promise<boolean>;
}
