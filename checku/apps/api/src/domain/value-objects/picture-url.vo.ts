export class PictureUrl {
  private readonly _value: string;

  constructor(value: string) {
    this.validate(value);
    this._value = value.trim();
  }

  get value(): string {
    return this._value;
  }

  private validate(url: string): void {
    if (!url || url.trim().length === 0) {
      throw new Error('Picture URL cannot be empty');
    }

    // Basic URL validation
    try {
      new URL(url.trim());
    } catch {
      throw new Error('Invalid picture URL format');
    }

    if (url.trim().length > 2048) {
      throw new Error('Picture URL is too long');
    }

    // Check if it's a valid image URL (basic check)
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
    const hasImageExtension = imageExtensions.some(ext => 
      url.toLowerCase().includes(ext)
    );
    
    // Allow URLs without extensions (like Google profile pictures)
    const isGoogleUserContent = url.includes('googleusercontent.com');
    const isGravatarUrl = url.includes('gravatar.com');
    
    if (!hasImageExtension && !isGoogleUserContent && !isGravatarUrl) {
      // Allow other common image hosting domains
      const allowedDomains = ['imgur.com', 'cloudinary.com', 'amazonaws.com'];
      const hasAllowedDomain = allowedDomains.some(domain => 
        url.includes(domain)
      );
      
      if (!hasAllowedDomain) {
        throw new Error('Picture URL must be a valid image URL');
      }
    }
  }

  equals(other: PictureUrl): boolean {
    return this._value === other._value;
  }

  toString(): string {
    return this._value;
  }
}
