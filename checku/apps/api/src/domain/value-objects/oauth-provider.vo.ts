export type OAuthProviderType = 'google' | 'linkedin' | 'local';

export class OAuthProvider {
  private readonly _type: OAuthProviderType;
  private readonly _providerId?: string;

  constructor(type: OAuthProviderType, providerId?: string) {
    this.validate(type, providerId);
    this._type = type;
    this._providerId = providerId;
  }

  get type(): OAuthProviderType {
    return this._type;
  }

  get providerId(): string | undefined {
    return this._providerId;
  }

  private validate(type: OAuthProviderType, providerId?: string): void {
    if (!type) {
      throw new Error('OAuth provider type is required');
    }

    const validTypes: OAuthProviderType[] = ['google', 'linkedin', 'local'];
    if (!validTypes.includes(type)) {
      throw new Error(`Invalid OAuth provider type: ${type}`);
    }

    if (type !== 'local' && !providerId) {
      throw new Error(`Provider ID is required for ${type} OAuth provider`);
    }

    if (type === 'local' && providerId) {
      throw new Error('Provider ID should not be provided for local provider');
    }

    if (providerId && providerId.trim().length === 0) {
      throw new Error('Provider ID cannot be empty');
    }
  }

  equals(other: OAuthProvider): boolean {
    return this._type === other._type && this._providerId === other._providerId;
  }

  toString(): string {
    return this._providerId ? `${this._type}:${this._providerId}` : this._type;
  }

  static local(): OAuthProvider {
    return new OAuthProvider('local');
  }

  static google(providerId: string): OAuthProvider {
    return new OAuthProvider('google', providerId);
  }

  static linkedin(providerId: string): OAuthProvider {
    return new OAuthProvider('linkedin', providerId);
  }
}
