import { randomBytes } from 'crypto';

export class UserId {
  private readonly _value: string;

  constructor(value?: string) {
    if (value) {
      this.validate(value);
      this._value = value;
    } else {
      this._value = this.generate();
    }
  }

  get value(): string {
    return this._value;
  }

  private validate(id: string): void {
    if (!id || id.trim().length === 0) {
      throw new Error('User ID cannot be empty');
    }

    // Very flexible validation - just check it's a reasonable string
    // This allows for various ID formats from the database
    if (id.length < 10 || id.length > 50) {
      throw new Error('Invalid User ID format');
    }

    // Allow alphanumeric characters, hyphens, and underscores
    if (!/^[a-zA-Z0-9_-]+$/.test(id)) {
      throw new Error('Invalid User ID format');
    }
  }

  private generate(): string {
    // Simple CUID-like generation for demo purposes
    // In production, you might want to use a proper CUID library
    const timestamp = Date.now().toString(36);
    const randomPart = randomBytes(12).toString('base64url').slice(0, 16);
    return `c${timestamp}${randomPart}`.slice(0, 25);
  }

  equals(other: UserId): boolean {
    return this._value === other._value;
  }

  toString(): string {
    return this._value;
  }

  static fromString(value: string): UserId {
    return new UserId(value);
  }
}
