// This file will be refactored - keeping temporarily for reference
import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';

// Define types that match our Prisma schema
export interface User {
  id: string;
  email: string;
  name: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserInput {
  email: string;
  name?: string;
}

export interface UpdateUserInput {
  email?: string;
  name?: string;
}

@Injectable()
export class UsersService {
  constructor(private readonly databaseService: DatabaseService) {}

  async createUser(data: CreateUserInput): Promise<User> {
    return this.databaseService.user.create({
      data,
    });
  }

  async findAllUsers(): Promise<User[]> {
    return this.databaseService.user.findMany();
  }

  async findUserById(id: string): Promise<User | null> {
    return this.databaseService.user.findUnique({
      where: { id },
    });
  }

  async findUserByEmail(email: string): Promise<User | null> {
    return this.databaseService.user.findUnique({
      where: { email },
    });
  }

  async updateUser(id: string, data: UpdateUserInput): Promise<User> {
    return this.databaseService.user.update({
      where: { id },
      data,
    });
  }

  async deleteUser(id: string): Promise<User> {
    return this.databaseService.user.delete({
      where: { id },
    });
  }
}
