import { Injectable, Inject } from '@nestjs/common';
import {
  User,
  Email,
  UserRepository,
  UserDomainService,
} from '../../../domain';
import { CreateUserDto, UserResponseDto } from '../../dtos';
import { USER_REPOSITORY } from '../../../shared/tokens';

@Injectable()
export class CreateUserUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
    private readonly userDomainService: UserDomainService,
  ) {}

  async execute(dto: CreateUserDto): Promise<UserResponseDto> {
    // Create domain objects
    const email = new Email(dto.email);
    const user = User.create({
      email,
      name: dto.name,
    });

    // Check if email already exists
    const existingUser = await this.userRepository.findByEmail(email);

    // Apply domain rules
    this.userDomainService.validateUserForCreation(user, existingUser);

    // Save to repository
    const savedUser = await this.userRepository.save(user);

    // Return response DTO
    return savedUser.toPlainObject();
  }
}
