import { Injectable, Inject } from '@nestjs/common';
import { UserId, UserRepository } from '../../../domain';
import { NotFoundException } from '../../../shared/exceptions';
import { USER_REPOSITORY } from '../../../shared/tokens';

@Injectable()
export class DeleteUserUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const userId = UserId.fromString(id);

    // Check if user exists
    const exists = await this.userRepository.exists(userId);
    if (!exists) {
      throw new NotFoundException('User', id);
    }

    // Delete user
    await this.userRepository.delete(userId);
  }
}
