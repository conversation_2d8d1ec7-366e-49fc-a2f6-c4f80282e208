import { Injectable, Inject } from '@nestjs/common';
import { UserRepository, OAuthProvider } from '../../../domain';
import { UserResponseDto } from '../../dtos';
import { NotFoundException } from '../../../shared/exceptions';
import { USER_REPOSITORY } from '../../../shared/tokens';

export interface OAuthSigninDto {
  provider: 'google' | 'linkedin';
  providerId: string;
}

@Injectable()
export class OAuthSigninUseCase {
  constructor(
    @Inject(USER_REPOSITORY)
    private readonly userRepository: UserRepository,
  ) {}

  async execute(dto: OAuthSigninDto): Promise<UserResponseDto> {
    // Create domain objects
    const provider =
      dto.provider === 'google'
        ? OAuthProvider.google(dto.providerId)
        : OAuthProvider.linkedin(dto.providerId);

    // Find user by OAuth provider
    const user = await this.userRepository.findByOAuthProvider(provider);

    if (!user) {
      throw new NotFoundException('User', `${dto.provider}:${dto.providerId}`);
    }

    // Return response DTO
    return user.toPlainObject();
  }
}
