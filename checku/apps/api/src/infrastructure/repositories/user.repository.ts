import { Injectable } from '@nestjs/common';
import {
  User,
  UserRepository,
  Email,
  UserId,
  OAuthProvider,
} from '../../domain';
import { DatabaseService } from '../../database/database.service';

@Injectable()
export class PrismaUserRepository implements UserRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  async save(user: User): Promise<User> {
    const userData = user.toPlainObject();

    const savedUser = await this.databaseService.user.upsert({
      where: { id: userData.id },
      update: {
        email: userData.email,
        name: userData.name,
        provider: userData.provider,
        providerId: userData.providerId,
        picture: userData.picture,
        updatedAt: userData.updatedAt,
      },
      create: {
        id: userData.id,
        email: userData.email,
        name: userData.name,
        provider: userData.provider,
        providerId: userData.providerId,
        picture: userData.picture,
        createdAt: userData.createdAt,
        updatedAt: userData.updatedAt,
      },
    });

    return User.fromPersistence(savedUser);
  }

  async findById(id: UserId): Promise<User | null> {
    const user = await this.databaseService.user.findUnique({
      where: { id: id.value },
    });

    if (!user) {
      return null;
    }

    return User.fromPersistence(user);
  }

  async findByEmail(email: Email): Promise<User | null> {
    const user = await this.databaseService.user.findUnique({
      where: { email: email.value },
    });

    if (!user) {
      return null;
    }

    return User.fromPersistence(user);
  }

  async findByOAuthProvider(provider: OAuthProvider): Promise<User | null> {
    console.log(
      'Looking for user with provider:',
      provider.type,
      'providerId:',
      provider.providerId,
    );

    const user = await this.databaseService.user.findFirst({
      where: {
        provider: provider.type,
        providerId: provider.providerId ?? '',
      },
    });

    console.log('Found user in database:', user);

    if (!user) {
      return null;
    }

    return User.fromPersistence(user);
  }

  async findAll(): Promise<User[]> {
    const users = await this.databaseService.user.findMany({
      orderBy: { createdAt: 'desc' },
    });

    return users.map((user) => User.fromPersistence(user));
  }

  async delete(id: UserId): Promise<void> {
    await this.databaseService.user.delete({
      where: { id: id.value },
    });
  }

  async exists(id: UserId): Promise<boolean> {
    const count = await this.databaseService.user.count({
      where: { id: id.value },
    });

    return count > 0;
  }

  async existsByEmail(email: Email): Promise<boolean> {
    const count = await this.databaseService.user.count({
      where: { email: email.value },
    });

    return count > 0;
  }

  async existsByOAuthProvider(provider: OAuthProvider): Promise<boolean> {
    const count = await this.databaseService.user.count({
      where: {
        provider: provider.type,
        providerId: provider.providerId ?? '',
      },
    });

    return count > 0;
  }
}
