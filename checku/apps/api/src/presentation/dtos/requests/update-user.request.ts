import { Is<PERSON><PERSON>, IsOptional, <PERSON>S<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class UpdateUserRequest {
  @IsOptional()
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email?: string;

  @IsOptional()
  @IsString({ message: 'Name must be a string' })
  @MaxLength(100, { message: 'Name cannot be longer than 100 characters' })
  name?: string;
}
