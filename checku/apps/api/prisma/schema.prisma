// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model with OAuth support
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?

  // OAuth fields
  provider     String?  // 'google', 'local', etc.
  providerId   String?  // OAuth provider's user ID
  picture      String?  // Profile picture URL

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([provider, providerId])
  @@map("users")
}
