# Database Setup with Prisma and PostgreSQL

This API uses Prisma ORM with PostgreSQL for database operations.

## Prerequisites

- PostgreSQL installed and running
- Node.js and pnpm installed

## Setup

### 1. Environment Variables

Copy the environment template and configure your database:

```bash
cp .env.example .env
```

Update the `DATABASE_URL` in `.env` with your PostgreSQL connection string:

```
DATABASE_URL="postgresql://username:password@localhost:5432/database_name?schema=public"
```

### 2. Database Setup

Create your PostgreSQL database:

```sql
CREATE DATABASE checku_db;
CREATE USER checku_user WITH PASSWORD 'checku_password';
GRANT ALL PRIVILEGES ON DATABASE checku_db TO checku_user;
```

### 3. Generate Prisma Client

```bash
pnpm db:generate
```

### 4. Run Migrations

```bash
pnpm db:migrate
```

### 5. Seed Database (Optional)

```bash
pnpm db:seed
```

## Available Scripts

- `pnpm db:generate` - Generate Prisma client
- `pnpm db:push` - Push schema changes to database (for development)
- `pnpm db:migrate` - Create and run migrations
- `pnpm db:migrate:deploy` - Deploy migrations (for production)
- `pnpm db:seed` - Seed database with initial data
- `pnpm db:studio` - Open Prisma Studio (database GUI)
- `pnpm db:reset` - Reset database and run migrations

## Schema

The database schema is defined in `prisma/schema.prisma`. After making changes:

1. Run `pnpm db:generate` to update the Prisma client
2. Run `pnpm db:migrate` to create and apply migrations

## Usage in Code

The `DatabaseService` is globally available and extends `PrismaClient`. Example usage:

```typescript
import { Injectable } from '@nestjs/common';
import { DatabaseService } from '../database/database.service';
import type { User, Prisma } from '@prisma/client';

@Injectable()
export class MyService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findUsers(): Promise<User[]> {
    return this.databaseService.user.findMany();
  }

  async createUser(data: Prisma.UserCreateInput): Promise<User> {
    return this.databaseService.user.create({ data });
  }
}
```

## Example API Endpoints

With the included Users module, you can test these endpoints:

- `GET /users` - Get all users
- `GET /users/:id` - Get user by ID
- `POST /users` - Create new user
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user

## Troubleshooting

### Connection Issues

1. Ensure PostgreSQL is running
2. Check your `DATABASE_URL` in `.env`
3. Verify database and user exist
4. Check firewall/network settings

### Migration Issues

1. Check database permissions
2. Ensure schema is up to date: `pnpm db:generate`
3. Reset if needed: `pnpm db:reset`

### Generated Client Issues

If you get import errors, regenerate the client:

```bash
pnpm db:generate
```
